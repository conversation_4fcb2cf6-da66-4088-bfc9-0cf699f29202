import React, { useContext, useEffect, useState } from 'react'
import { UserContext } from '../../../context/UserProvider'
import { Avatar, Divider, IconButton, List, ListItem, ListItemAvatar, ListItemSecondaryAction, ListItemText, makeStyles, Typography } from '@material-ui/core';
import { Settings } from '@material-ui/icons';
import { svgKindIO } from '../../../constants/globalConst';
import { withRouter } from 'react-router-dom';
import { ALL_PATHS } from '../../../constants/routerConst';
// Importa imágenes de módulos
import masterImg from '../../../assets/node.png';
import ambientImg from '../../../assets/Ambiente.png';
import waterImg from '../../../assets/Agua.png';

// Estilos personalizados
const useStyles = makeStyles(theme => ({
  root: {
    width: '100%',
    backgroundColor: theme.palette.background.paper,
  },
  moduleType: {
    fontWeight: theme.typography.fontWeightMedium,
  },
  primaryText: {
    display: 'flex',
    alignItems: 'center',
    '& > span': {
      marginLeft: theme.spacing(1),
    },
  },
}));



// Mapea tipos de módulo a etiquetas
const MODULE_LABELS = {
  0: 'Módulo Maestro',
  1: 'Módulo Ambiente',
  2: 'Módulo de Agua',
};

const MODULE_IMAGES = {
  0: masterImg,
  1: ambientImg,
  2: waterImg,
};

const ModuleList = (props) => {
	const {dataMacCan,currentMac} = useContext(UserContext)
	const classes = useStyles();
	const [modules, setModules] = useState([])
	useEffect(() => {
	  if(currentMac !== "" && dataMacCan.length !== 0) {
		const modulesData = dataMacCan.find((element) => {
			return element.mac === currentMac;
		})
		const newModulesData = modulesData.cans.map((item) => {
			return {
				...item,
				uid: `${currentMac}@${item.id}@N`
			}
		})
		setModules(modulesData.cans);
		console.log("Esto es modulesData:",newModulesData);
	  } else {
		setModules([]);
	  }
	}, [currentMac,dataMacCan])

	const onModuleSelect = (module) => {
		console.log("Esto es module:",module);
		// Crear el uid para la navegación
		const uid = `${currentMac}@${module.id}@N`;
		// Navegar a la configuración del módulo
		const route = `${ALL_PATHS.PATH_CONFIG}/Tree@${uid}`;
		props.history.push(route);
	}
	
  return (
	<>
		{/* Título de la lista de módulos */}
		<Typography variant="h6" className={classes.title}>
			Módulos Asociados
		</Typography>
	
		<List className={classes.root}>
		{modules?.map((module, index) => (
			<React.Fragment key={module.id}>
			<ListItem button onClick={() => onModuleSelect(module)}>
				<ListItemAvatar>
				<Avatar alt={MODULE_LABELS[module.type]} src={MODULE_IMAGES[module.type]} />
				</ListItemAvatar>

				<ListItemText
				primary={
					<div className={classes.primaryText}>
					<Typography className={classes.moduleType} variant="subtitle1">
						{MODULE_LABELS[module.type]} | CAN-Bus: {module.id}
					</Typography>
					{/* <Typography component="span" variant="subtitle1">
						CAN-Bus: {module.id}
					</Typography> */}
					</div>
				}
				secondary={
					<Typography component="span" variant="body2">
					MAC: {currentMac}
					</Typography>
				}
				/>

				{/* <ListItemSecondaryAction>
				<IconButton
					edge="end"
					aria-label="configuraciones"
					onClick={() => onModuleSelect(module)}
				>
					<Settings />
				</IconButton>
				</ListItemSecondaryAction> */}
			</ListItem>

			{/* Línea divisora entre elementos */}
			{index < modules.length - 1 && <Divider component="li" />}
			</React.Fragment>
		))}
		</List>
	</>
  )
}

const ModuleListWithRouter = withRouter(ModuleList);

export { ModuleListWithRouter as ModuleList };
export default ModuleListWithRouter;
